import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AudioSessionManager from '../services/AudioSessionManager';

/**
 * Audio Mixing Settings Component
 * Allows users to control how the app's audio interacts with other device audio
 */
const AudioMixingSettings = ({ visible, onClose }) => {
  const [currentMode, setCurrentMode] = useState('mix');
  const [isChanging, setIsChanging] = useState(false);

  useEffect(() => {
    if (visible) {
      loadCurrentMode();
    }
  }, [visible]);

  const loadCurrentMode = () => {
    const mode = AudioSessionManager.getAudioMixingMode();
    setCurrentMode(mode);
  };

  const handleModeChange = async (newMode) => {
    if (newMode === currentMode || isChanging) return;

    try {
      setIsChanging(true);
      
      console.log('[AUDIO_SETTINGS] Changing audio mode to:', newMode);
      await AudioSessionManager.setAudioMode(newMode);
      
      setCurrentMode(newMode);
      
      // Show confirmation
      const modeNames = {
        mix: 'Mix with Other Audio',
        duck: 'Duck Other Audio',
        exclusive: 'Exclusive Control'
      };
      
      Alert.alert(
        'Audio Mode Changed',
        `Audio mixing mode changed to "${modeNames[newMode]}". This will affect how videos play alongside other apps' audio.`,
        [{ text: 'OK' }]
      );
      
    } catch (error) {
      console.error('[AUDIO_SETTINGS] Failed to change audio mode:', error);
      Alert.alert(
        'Error',
        'Failed to change audio mixing mode. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsChanging(false);
    }
  };

  const renderModeOption = (mode) => {
    const isSelected = currentMode === mode.id;
    
    return (
      <TouchableOpacity
        key={mode.id}
        style={[styles.modeOption, isSelected && styles.selectedMode]}
        onPress={() => handleModeChange(mode.id)}
        disabled={isChanging}
      >
        <View style={styles.modeHeader}>
          <View style={styles.modeIconContainer}>
            <Ionicons 
              name={mode.icon} 
              size={24} 
              color={isSelected ? '#6c5ce7' : '#666'} 
            />
          </View>
          <View style={styles.modeInfo}>
            <Text style={[styles.modeName, isSelected && styles.selectedText]}>
              {mode.name}
            </Text>
            <Text style={styles.modeDescription}>
              {mode.description}
            </Text>
          </View>
          {isSelected && (
            <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const modes = AudioSessionManager.getAvailableModes();

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Audio Mixing Settings</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>How should videos interact with other audio?</Text>
            <Text style={styles.sectionDescription}>
              Choose how the app's video audio should behave when other apps are playing music, podcasts, or other audio.
            </Text>
          </View>

          <View style={styles.modesContainer}>
            {modes.map(renderModeOption)}
          </View>

          <View style={styles.infoSection}>
            <View style={styles.infoHeader}>
              <Ionicons name="information-circle" size={20} color="#6c5ce7" />
              <Text style={styles.infoTitle}>About Concurrent Playback</Text>
            </View>
            <Text style={styles.infoText}>
              This app supports playing multiple videos simultaneously. The audio mixing setting controls how video audio interacts with other apps on your device.
            </Text>
            
            <View style={styles.featureList}>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark" size={16} color="#4CAF50" />
                <Text style={styles.featureText}>Multiple videos can play at once</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark" size={16} color="#4CAF50" />
                <Text style={styles.featureText}>Videos continue playing in background</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark" size={16} color="#4CAF50" />
                <Text style={styles.featureText}>Configurable audio mixing behavior</Text>
              </View>
            </View>
          </View>

          <View style={styles.warningSection}>
            <View style={styles.warningHeader}>
              <Ionicons name="warning" size={20} color="#FF9800" />
              <Text style={styles.warningTitle}>Battery Usage</Text>
            </View>
            <Text style={styles.warningText}>
              Playing multiple videos simultaneously may increase battery usage. Consider pausing videos when not actively watching them.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  modesContainer: {
    backgroundColor: 'white',
    marginBottom: 16,
  },
  modeOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedMode: {
    backgroundColor: '#f8f7ff',
    borderLeftWidth: 3,
    borderLeftColor: '#6c5ce7',
  },
  modeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modeIconContainer: {
    width: 40,
    alignItems: 'center',
  },
  modeInfo: {
    flex: 1,
    marginLeft: 12,
  },
  modeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  selectedText: {
    color: '#6c5ce7',
  },
  modeDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  infoSection: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  featureList: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
  },
  warningSection: {
    backgroundColor: '#fff3e0',
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 3,
    borderLeftColor: '#FF9800',
  },
  warningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF9800',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default AudioMixingSettings;
