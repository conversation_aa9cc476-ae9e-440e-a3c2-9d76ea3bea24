import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import VideoQualitySelector from '../VideoQualitySelector';
import UrlResolutionService from '../../services/UrlResolutionService';
import BackgroundDownloadService from '../../services/BackgroundDownloadService';
import PermissionManager from '../../utils/PermissionManager';


const { width, height } = Dimensions.get('window');

const VideoThumbnailContextMenu = ({
  visible,
  onClose,
  thumbnailData,
  onDownloadRequest
}) => {
  const [showQualitySelector, setShowQualitySelector] = useState(false);

  console.log('[CONTEXT_MENU] Component rendered with:', { visible, thumbnailData });

  if (!thumbnailData) {
    console.log('[CONTEXT_MENU] No thumbnail data - returning null');
    return null;
  }

  // Validate thumbnail data
  const isValidThumbnailData = thumbnailData.videoUrl &&
    (thumbnailData.videoUrl.startsWith('http') || thumbnailData.videoUrl.startsWith('https'));

  if (!isValidThumbnailData) {
    console.log('[CONTEXT_MENU] Invalid thumbnail data:', thumbnailData);
  }



  const handleDownloadVideo = async () => {
    if (!thumbnailData.videoUrl) {
      Alert.alert('Erro', 'URL do vídeo não encontrada');
      return;
    }

    const permissionResult = await PermissionManager.ensurePermissions();
    if (!permissionResult.success) return;

    try {
      onClose();

      console.log('🚀 Iniciando download rápido de thumbnail:', thumbnailData);

      // Processar URL antes do download
      const processedUrl = await processVideoUrl(thumbnailData.videoUrl, thumbnailData.platform);

      // Iniciar download em background
      const downloadId = await BackgroundDownloadService.startBackgroundDownload({
        url: processedUrl,
        originalUrl: thumbnailData.videoUrl,
        quality: { quality: 'best', label: 'Melhor Qualidade' },
        platform: thumbnailData.platform,
        title: thumbnailData.title
      });

      Alert.alert(
        'Download Iniciado',
        `Download do vídeo "${thumbnailData.title || 'Vídeo'}" foi iniciado em segundo plano.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('❌ Erro no download rápido:', error);
      Alert.alert(
        'Erro no Download',
        error.message || 'Erro ao iniciar download. Tente novamente.',
        [{ text: 'OK' }]
      );
    }
  };

  // Enhanced video URL processing using UrlResolutionService
  const processVideoUrl = async (url, platform) => {
    try {
      console.log('Processing video URL:', url, 'Platform:', platform);

      // Use the comprehensive URL resolution service
      const processedUrl = await UrlResolutionService.processVideoUrl(url, platform);

      console.log('Processed URL:', processedUrl);
      return processedUrl;
    } catch (error) {
      console.error('Error processing video URL:', error);
      return url; // Return original URL as fallback
    }
  };

  const handleDownloadWithQuality = async () => {
    if (!thumbnailData.videoUrl) {
      Alert.alert(
        'Erro',
        'Não foi possível encontrar o link do vídeo. Tente navegar para a página do vídeo primeiro.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      // Validar e processar URL antes de mostrar seletor de qualidade
      const processedUrl = await processVideoUrl(thumbnailData.videoUrl, thumbnailData.platform);

      if (!processedUrl || processedUrl === thumbnailData.videoUrl) {
        console.log('⚠️ URL não processada, usando URL original');
      }

      onClose();
      setShowQualitySelector(true);
    } catch (error) {
      console.error('❌ Erro ao processar URL:', error);
      Alert.alert(
        'Erro',
        'Erro ao processar o link do vídeo. Tente novamente.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleQualitySelected = async (quality) => {
    const permissionResult = await PermissionManager.ensurePermissions();
    if (!permissionResult.success) return;

    try {
      console.log('🚀 Iniciando download de thumbnail com qualidade:', quality);

      // Enhanced URL processing
      const downloadUrl = quality.url;
      const processedUrl = downloadUrl ?
        await processVideoUrl(downloadUrl, thumbnailData.platform) :
        await processVideoUrl(thumbnailData.videoUrl, thumbnailData.platform);

      // Iniciar download em background
      const downloadId = await BackgroundDownloadService.startBackgroundDownload({
        url: processedUrl,
        originalUrl: thumbnailData.videoUrl,
        quality: quality,
        platform: thumbnailData.platform,
        title: thumbnailData.title
      });

      // Fechar modal de qualidade
      setShowQualitySelector(false);

      // Mostrar confirmação
      Alert.alert(
        'Download Iniciado',
        `Download do vídeo "${thumbnailData.title || 'Vídeo'}" em qualidade ${quality.quality} foi iniciado em segundo plano.`,
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('❌ Erro no download:', error);
      Alert.alert(
        'Erro no Download',
        error.message || 'Não foi possível iniciar o download. Tente novamente.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleOpenInNewTab = () => {
    if (thumbnailData.videoUrl) {
      // This would need to be implemented in the parent component
      Alert.alert('Info', 'Funcionalidade de nova aba será implementada');
      onClose();
    }
  };

  const handleCopyLink = async () => {
    if (thumbnailData.videoUrl) {
      try {
        // For React Native, we would use Clipboard API
        // import { Clipboard } from 'react-native';
        // await Clipboard.setString(thumbnailData.videoUrl);

        // For now, show the URL in an alert for the user to copy manually
        Alert.alert(
          'Link do Vídeo',
          thumbnailData.videoUrl,
          [
            { text: 'Fechar', style: 'cancel' },
            { text: 'OK' }
          ]
        );
        onClose();
      } catch (error) {
        Alert.alert('Erro', 'Não foi possível copiar o link');
      }
    }
  };

  console.log('[CONTEXT_MENU] Rendering modal with visible:', visible);

  return (
    <>
      <Modal
        visible={visible}
        transparent={true}
        animationType="fade"
        onRequestClose={onClose}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={onClose}
        >
          <View style={[
            styles.contextMenu,
            {
              left: Math.min(thumbnailData.x || 0, width - 250),
              top: Math.min(thumbnailData.y || 0, height - 300)
            }
          ]}>
            {/* Video Info */}
            <View style={styles.videoInfo}>
              <View style={styles.videoIcon}>
                <Ionicons name="play-circle" size={24} color="#6c5ce7" />
              </View>
              <View style={styles.videoDetails}>
                <Text style={styles.videoTitle} numberOfLines={2}>
                  {thumbnailData.title || 'Vídeo'}
                </Text>
                {thumbnailData.duration && (
                  <Text style={styles.videoDuration}>
                    {thumbnailData.duration}
                  </Text>
                )}
              </View>
            </View>

            <View style={styles.separator} />

            {/* Menu Options */}
            <ScrollView style={styles.menuOptions}>
              <TouchableOpacity
                style={styles.menuItem}
                onPress={handleDownloadVideo}
              >
                <Ionicons name="download" size={20} color="#4CAF50" />
                <Text style={styles.menuItemText}>Download Rápido</Text>
                <Text style={styles.menuItemSubtext}>Melhor qualidade disponível</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.menuItem}
                onPress={handleDownloadWithQuality}
              >
                <Ionicons name="options" size={20} color="#2196F3" />
                <Text style={styles.menuItemText}>Escolher Qualidade</Text>
                <Text style={styles.menuItemSubtext}>Selecionar resolução</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.menuItem}
                onPress={handleOpenInNewTab}
              >
                <Ionicons name="open" size={20} color="#FF9800" />
                <Text style={styles.menuItemText}>Abrir em Nova Aba</Text>
                <Text style={styles.menuItemSubtext}>Navegar para o vídeo</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.menuItem}
                onPress={handleCopyLink}
              >
                <Ionicons name="copy" size={20} color="#9C27B0" />
                <Text style={styles.menuItemText}>Copiar Link</Text>
                <Text style={styles.menuItemSubtext}>Copiar URL do vídeo</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Quality Selector Modal */}
      <VideoQualitySelector
        visible={showQualitySelector}
        onClose={() => setShowQualitySelector(false)}
        videoUrl={thumbnailData?.videoUrl}
        onQualitySelected={handleQualitySelected}
        onDownloadStart={handleQualitySelected}
      />
    </>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  contextMenu: {
    position: 'absolute',
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    minWidth: 250,
    maxWidth: 300,
    maxHeight: 400,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#2d2d44',
  },
  videoInfo: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  videoIcon: {
    marginRight: 12,
  },
  videoDetails: {
    flex: 1,
  },
  videoTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 18,
  },
  videoDuration: {
    color: '#888',
    fontSize: 12,
    marginTop: 4,
  },
  separator: {
    height: 1,
    backgroundColor: '#2d2d44',
    marginHorizontal: 16,
  },
  menuOptions: {
    maxHeight: 300,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  menuItemText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
    flex: 1,
  },
  menuItemSubtext: {
    color: '#888',
    fontSize: 12,
    marginLeft: 12,
    flex: 1,
    marginTop: 2,
  },
});

export default VideoThumbnailContextMenu;
