import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { Video } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import AudioSessionManager from '../services/AudioSessionManager';

const { width } = Dimensions.get('window');

/**
 * Concurrent Video Player Component
 * Supports playing multiple videos simultaneously without interrupting other device audio
 */
const ConcurrentVideoPlayer = ({
  source,
  title,
  style,
  onPlaybackStatusUpdate,
  onError,
  onLoad,
  autoPlay = false,
  showControls = true,
  resizeMode = 'contain',
  isLooping = false,
  volume = 1.0,
  isMuted = false,
  playerId = null
}) => {
  const videoRef = useRef(null);
  const [status, setStatus] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);

  useEffect(() => {
    initializeAudioSession();
    return () => {
      // Cleanup when component unmounts
      if (videoRef.current) {
        videoRef.current.unloadAsync();
      }
    };
  }, []);

  useEffect(() => {
    if (videoRef.current && source) {
      loadVideo();
    }
  }, [source]);

  const initializeAudioSession = async () => {
    try {
      await AudioSessionManager.prepareForVideoPlayback();
      console.log('[VIDEO_PLAYER] Audio session prepared for concurrent playback');
    } catch (error) {
      console.error('[VIDEO_PLAYER] Failed to prepare audio session:', error);
    }
  };

  const loadVideo = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('[VIDEO_PLAYER] Loading video:', source);
      
      const initialStatus = {
        shouldPlay: autoPlay,
        isLooping: isLooping,
        volume: volume,
        isMuted: isMuted,
      };

      await videoRef.current.loadAsync(source, initialStatus);
      
      if (onLoad) {
        onLoad();
      }
    } catch (error) {
      console.error('[VIDEO_PLAYER] Error loading video:', error);
      setError(error.message);
      if (onError) {
        onError(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlaybackStatusUpdate = (playbackStatus) => {
    setStatus(playbackStatus);
    
    if (playbackStatus.isLoaded) {
      setIsPlaying(playbackStatus.isPlaying);
      setIsLoading(false);
    }
    
    if (playbackStatus.error) {
      setError(playbackStatus.error);
      console.error('[VIDEO_PLAYER] Playback error:', playbackStatus.error);
    }
    
    if (onPlaybackStatusUpdate) {
      onPlaybackStatusUpdate(playbackStatus);
    }
  };

  const togglePlayPause = async () => {
    try {
      if (!videoRef.current) return;
      
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        // Ensure audio session is ready for concurrent playback
        await AudioSessionManager.prepareForVideoPlayback();
        await videoRef.current.playAsync();
      }
    } catch (error) {
      console.error('[VIDEO_PLAYER] Error toggling play/pause:', error);
      Alert.alert('Playback Error', 'Failed to control video playback');
    }
  };

  const handleSeek = async (position) => {
    try {
      if (videoRef.current && status.isLoaded) {
        await videoRef.current.setPositionAsync(position);
      }
    } catch (error) {
      console.error('[VIDEO_PLAYER] Error seeking:', error);
    }
  };

  const toggleMute = async () => {
    try {
      if (videoRef.current) {
        await videoRef.current.setIsMutedAsync(!status.isMuted);
      }
    } catch (error) {
      console.error('[VIDEO_PLAYER] Error toggling mute:', error);
    }
  };

  const formatTime = (millis) => {
    const totalSeconds = Math.floor(millis / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (error) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#ff4444" />
          <Text style={styles.errorText}>Failed to load video</Text>
          <Text style={styles.errorDetails}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadVideo}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {title && (
        <View style={styles.titleContainer}>
          <Text style={styles.title} numberOfLines={2}>{title}</Text>
        </View>
      )}
      
      <View style={styles.videoContainer}>
        <Video
          ref={videoRef}
          style={styles.video}
          resizeMode={resizeMode}
          onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
          useNativeControls={false}
          shouldPlay={false} // We control playback manually for better concurrent support
        />
        
        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#6c5ce7" />
            <Text style={styles.loadingText}>Loading video...</Text>
          </View>
        )}
        
        {showControls && !isLoading && (
          <View style={styles.controlsOverlay}>
            <TouchableOpacity style={styles.playButton} onPress={togglePlayPause}>
              <Ionicons 
                name={isPlaying ? "pause" : "play"} 
                size={32} 
                color="white" 
              />
            </TouchableOpacity>
            
            <View style={styles.bottomControls}>
              <TouchableOpacity style={styles.controlButton} onPress={toggleMute}>
                <Ionicons 
                  name={status.isMuted ? "volume-mute" : "volume-high"} 
                  size={20} 
                  color="white" 
                />
              </TouchableOpacity>
              
              {status.isLoaded && (
                <View style={styles.timeContainer}>
                  <Text style={styles.timeText}>
                    {formatTime(status.positionMillis || 0)} / {formatTime(status.durationMillis || 0)}
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}
      </View>
      
      {playerId && (
        <View style={styles.playerInfo}>
          <Text style={styles.playerIdText}>Player: {playerId}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000',
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: 8,
  },
  titleContainer: {
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  title: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  videoContainer: {
    position: 'relative',
    aspectRatio: 16/9,
  },
  video: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    marginTop: 8,
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 32,
    padding: 16,
  },
  bottomControls: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 20,
    padding: 8,
    marginRight: 8,
  },
  timeContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  timeText: {
    color: 'white',
    fontSize: 12,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  playerInfo: {
    padding: 8,
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(108, 92, 231, 0.3)',
  },
  playerIdText: {
    color: '#6c5ce7',
    fontSize: 12,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  errorText: {
    color: '#ff4444',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 8,
  },
  errorDetails: {
    color: '#999',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
  retryButton: {
    backgroundColor: '#6c5ce7',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    marginTop: 12,
  },
  retryText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default ConcurrentVideoPlayer;
