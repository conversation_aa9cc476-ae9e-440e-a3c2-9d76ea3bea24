import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Modal,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

const Settings = ({ visible, onClose }) => {
  const [settings, setSettings] = useState({
    adBlockEnabled: true,
    trackingProtection: true,
    cookieBlocking: false,
    javascriptEnabled: true,
    imagesEnabled: true,
    popupBlocking: true,
    privateBrowsing: false,
    autoDownloadQuality: 'best',
    downloadLocation: 'gallery',
    clearDataOnExit: false
  });

  const [showAbout, setShowAbout] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('browser_settings');
      if (savedSettings) {
        setSettings({ ...settings, ...JSON.parse(savedSettings) });
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem('browser_settings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
    }
  };

  const updateSetting = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  const clearBrowsingData = () => {
    Alert.alert(
      'Limpar Dados de Navegação',
      'Isso irá remover histórico, cookies, cache e dados de sites. Continuar?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpar',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.multiRemove([
                'browser_history',
                'browser_bookmarks',
                'browser_cookies'
              ]);
              Alert.alert('Sucesso', 'Dados de navegação limpos');
            } catch (error) {
              Alert.alert('Erro', 'Não foi possível limpar os dados');
            }
          }
        }
      ]
    );
  };

  const clearDownloads = () => {
    Alert.alert(
      'Limpar Lista de Downloads',
      'Isso irá remover a lista de downloads (os arquivos não serão excluídos). Continuar?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpar',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('video_downloads');
              Alert.alert('Sucesso', 'Lista de downloads limpa');
            } catch (error) {
              Alert.alert('Erro', 'Não foi possível limpar a lista');
            }
          }
        }
      ]
    );
  };

  const SettingItem = ({ 
    icon, 
    title, 
    subtitle, 
    value, 
    onValueChange, 
    type = 'switch',
    onPress 
  }) => (
    <TouchableOpacity 
      style={styles.settingItem}
      onPress={onPress}
      disabled={type === 'switch'}
    >
      <View style={styles.settingIcon}>
        <Ionicons name={icon} size={24} color="#6c5ce7" />
      </View>
      
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#2d2d44', true: '#6c5ce7' }}
          thumbColor={value ? '#fff' : '#888'}
        />
      )}
      
      {type === 'arrow' && (
        <Ionicons name="chevron-forward" size={20} color="#888" />
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.title}>Configurações</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.content}>
          {/* Seção de Privacidade */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Privacidade e Segurança</Text>
            
            <SettingItem
              icon="shield-checkmark"
              title="Bloqueador de Anúncios"
              subtitle="Bloquear anúncios e rastreadores"
              value={settings.adBlockEnabled}
              onValueChange={(value) => updateSetting('adBlockEnabled', value)}
            />
            
            <SettingItem
              icon="eye-off"
              title="Proteção contra Rastreamento"
              subtitle="Impedir sites de rastrear sua atividade"
              value={settings.trackingProtection}
              onValueChange={(value) => updateSetting('trackingProtection', value)}
            />
            
            <SettingItem
              icon="ban"
              title="Bloquear Cookies"
              subtitle="Bloquear cookies de terceiros"
              value={settings.cookieBlocking}
              onValueChange={(value) => updateSetting('cookieBlocking', value)}
            />
            
            <SettingItem
              icon="close-circle"
              title="Bloquear Pop-ups"
              subtitle="Impedir janelas pop-up"
              value={settings.popupBlocking}
              onValueChange={(value) => updateSetting('popupBlocking', value)}
            />
            
            <SettingItem
              icon="glasses"
              title="Navegação Privada"
              subtitle="Não salvar histórico nem cookies"
              value={settings.privateBrowsing}
              onValueChange={(value) => updateSetting('privateBrowsing', value)}
            />
          </View>

          {/* Seção de Conteúdo */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Conteúdo</Text>
            
            <SettingItem
              icon="code-slash"
              title="JavaScript"
              subtitle="Permitir execução de JavaScript"
              value={settings.javascriptEnabled}
              onValueChange={(value) => updateSetting('javascriptEnabled', value)}
            />
            
            <SettingItem
              icon="image"
              title="Imagens"
              subtitle="Carregar imagens automaticamente"
              value={settings.imagesEnabled}
              onValueChange={(value) => updateSetting('imagesEnabled', value)}
            />
          </View>

          {/* Seção de Downloads */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Downloads</Text>
            
            <SettingItem
              icon="download"
              title="Qualidade Automática"
              subtitle={`Padrão: ${settings.autoDownloadQuality}`}
              type="arrow"
              onPress={() => {
                Alert.alert(
                  'Qualidade Padrão',
                  'Escolha a qualidade padrão para downloads',
                  [
                    { text: 'Melhor', onPress: () => updateSetting('autoDownloadQuality', 'best') },
                    { text: 'Média', onPress: () => updateSetting('autoDownloadQuality', 'medium') },
                    { text: 'Automática', onPress: () => updateSetting('autoDownloadQuality', 'auto') },
                    { text: 'Cancelar', style: 'cancel' }
                  ]
                );
              }}
            />
            
            <SettingItem
              icon="folder"
              title="Local de Download"
              subtitle="Salvar na galeria do dispositivo"
              type="arrow"
            />
          </View>

          {/* Seção de Dados */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Dados</Text>
            
            <SettingItem
              icon="trash"
              title="Limpar Dados de Navegação"
              subtitle="Histórico, cookies, cache"
              type="arrow"
              onPress={clearBrowsingData}
            />
            
            <SettingItem
              icon="list"
              title="Limpar Lista de Downloads"
              subtitle="Remove apenas a lista, não os arquivos"
              type="arrow"
              onPress={clearDownloads}
            />
            
            <SettingItem
              icon="exit"
              title="Limpar ao Sair"
              subtitle="Limpar dados automaticamente ao fechar"
              value={settings.clearDataOnExit}
              onValueChange={(value) => updateSetting('clearDataOnExit', value)}
            />
          </View>

          {/* Seção de Vídeo */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Reprodução de Vídeo</Text>

            <SettingItem
              icon="play-circle"
              title="Player de Vídeo Concorrente"
              subtitle="Reproduzir múltiplos vídeos simultaneamente"
              type="arrow"
              onPress={() => {
                Alert.alert(
                  'Player de Vídeo',
                  'Abrir o player de vídeo com suporte a reprodução concorrente?',
                  [
                    { text: 'Cancelar', style: 'cancel' },
                    {
                      text: 'Abrir',
                      onPress: () => {
                        // TODO: Navigate to video player screen
                        console.log('Opening concurrent video player');
                        Alert.alert('Info', 'Funcionalidade de navegação será implementada');
                      }
                    }
                  ]
                );
              }}
            />

            <SettingItem
              icon="musical-notes"
              title="Configurações de Áudio"
              subtitle="Controlar mixagem com outros apps"
              type="arrow"
              onPress={() => {
                Alert.alert(
                  'Configurações de Áudio',
                  'Escolha como o áudio dos vídeos deve interagir com outros apps:\n\n• Mix: Reproduzir junto com outros áudios\n• Duck: Diminuir volume de outros apps\n• Exclusivo: Pausar outros áudios',
                  [{ text: 'OK' }]
                );
              }}
            />
          </View>

          {/* Seção Sobre */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Sobre</Text>
            
            <SettingItem
              icon="information-circle"
              title="Sobre o Aplicativo"
              subtitle="Versão 1.0.0"
              type="arrow"
              onPress={() => setShowAbout(true)}
            />
            
            <SettingItem
              icon="help-circle"
              title="Ajuda e Suporte"
              subtitle="Obter ajuda"
              type="arrow"
            />
          </View>
        </ScrollView>

        {/* Modal Sobre */}
        <Modal
          visible={showAbout}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowAbout(false)}
        >
          <View style={styles.aboutOverlay}>
            <View style={styles.aboutContainer}>
              <View style={styles.aboutHeader}>
                <Text style={styles.aboutTitle}>Video Browser</Text>
                <TouchableOpacity onPress={() => setShowAbout(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.aboutContent}>
                <Text style={styles.aboutText}>
                  Video Browser é um navegador completo com capacidades avançadas de download de vídeos.
                </Text>
                
                <Text style={styles.aboutSection}>Recursos:</Text>
                <Text style={styles.aboutFeature}>• Navegação com múltiplas abas</Text>
                <Text style={styles.aboutFeature}>• Download de vídeos de qualquer site</Text>
                <Text style={styles.aboutFeature}>• Seleção de qualidade de vídeo</Text>
                <Text style={styles.aboutFeature}>• Bloqueador de anúncios integrado</Text>
                <Text style={styles.aboutFeature}>• Proteção de privacidade avançada</Text>
                <Text style={styles.aboutFeature}>• Gerenciador de downloads</Text>
                <Text style={styles.aboutFeature}>• Sistema de favoritos</Text>
                
                <Text style={styles.aboutVersion}>Versão 1.0.0</Text>
                <Text style={styles.aboutCopyright}>© 2024 Video Browser</Text>
              </ScrollView>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
    backgroundColor: '#1a1a2e',
  },
  title: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    color: '#6c5ce7',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    marginHorizontal: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    backgroundColor: '#2d2d44',
    marginHorizontal: 20,
    marginBottom: 2,
    borderRadius: 8,
  },
  settingIcon: {
    marginRight: 15,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  settingSubtitle: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  aboutOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  aboutContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    width: width * 0.9,
    maxHeight: '80%',
  },
  aboutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  aboutTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  aboutContent: {
    padding: 20,
  },
  aboutText: {
    color: '#fff',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
  },
  aboutSection: {
    color: '#6c5ce7',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  aboutFeature: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 5,
  },
  aboutVersion: {
    color: '#888',
    fontSize: 14,
    marginTop: 20,
    textAlign: 'center',
  },
  aboutCopyright: {
    color: '#888',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 5,
  },
});

export default Settings;
