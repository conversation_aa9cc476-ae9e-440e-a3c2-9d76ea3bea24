import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import ConcurrentVideoPlayer from '../components/ConcurrentVideoPlayer';
import AudioMixingSettings from '../components/AudioMixingSettings';
import AudioSessionManager from '../services/AudioSessionManager';

/**
 * Video Player Screen with Concurrent Playback Support
 * Demonstrates multiple videos playing simultaneously with configurable audio mixing
 */
const VideoPlayerScreen = ({ navigation, route }) => {
  const [videos, setVideos] = useState([]);
  const [showAudioSettings, setShowAudioSettings] = useState(false);
  const [audioMode, setAudioMode] = useState('mix');

  useEffect(() => {
    initializeScreen();
    loadAudioMode();
  }, []);

  const initializeScreen = async () => {
    try {
      // Initialize audio session for concurrent playback
      await AudioSessionManager.initialize();
      
      // Load initial video if passed from route params
      if (route?.params?.video) {
        addVideo(route.params.video);
      } else {
        // Add some demo videos for testing
        addDemoVideos();
      }
    } catch (error) {
      console.error('[VIDEO_SCREEN] Failed to initialize:', error);
      Alert.alert('Error', 'Failed to initialize video player');
    }
  };

  const loadAudioMode = () => {
    const mode = AudioSessionManager.getAudioMixingMode();
    setAudioMode(mode);
  };

  const addDemoVideos = () => {
    const demoVideos = [
      {
        id: '1',
        title: 'Sample Video 1',
        source: { uri: 'https://www.w3schools.com/html/mov_bbb.mp4' },
      },
      {
        id: '2',
        title: 'Sample Video 2',
        source: { uri: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4' },
      }
    ];
    
    setVideos(demoVideos);
  };

  const addVideo = (videoData) => {
    const newVideo = {
      id: Date.now().toString(),
      title: videoData.title || 'Untitled Video',
      source: videoData.source,
      ...videoData
    };
    
    setVideos(prev => [...prev, newVideo]);
  };

  const removeVideo = (videoId) => {
    Alert.alert(
      'Remove Video',
      'Are you sure you want to remove this video from the player?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setVideos(prev => prev.filter(video => video.id !== videoId));
          }
        }
      ]
    );
  };

  const addCustomVideo = () => {
    Alert.prompt(
      'Add Video',
      'Enter video URL:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Add',
          onPress: (url) => {
            if (url && url.trim()) {
              addVideo({
                title: 'Custom Video',
                source: { uri: url.trim() }
              });
            }
          }
        }
      ],
      'plain-text',
      'https://www.w3schools.com/html/mov_bbb.mp4'
    );
  };

  const handleVideoError = (error, videoId) => {
    console.error('[VIDEO_SCREEN] Video error:', error, 'for video:', videoId);
    Alert.alert(
      'Video Error',
      `Failed to load video: ${error.message || 'Unknown error'}`,
      [
        { text: 'OK' },
        {
          text: 'Remove Video',
          style: 'destructive',
          onPress: () => removeVideo(videoId)
        }
      ]
    );
  };

  const getAudioModeIcon = () => {
    switch (audioMode) {
      case 'mix': return 'musical-notes';
      case 'duck': return 'volume-medium';
      case 'exclusive': return 'volume-high';
      default: return 'musical-notes';
    }
  };

  const getAudioModeText = () => {
    switch (audioMode) {
      case 'mix': return 'Mix with Other Audio';
      case 'duck': return 'Duck Other Audio';
      case 'exclusive': return 'Exclusive Control';
      default: return 'Mix with Other Audio';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Concurrent Video Player</Text>
        
        <TouchableOpacity 
          style={styles.settingsButton}
          onPress={() => setShowAudioSettings(true)}
        >
          <Ionicons name="settings" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.audioModeIndicator}>
        <Ionicons name={getAudioModeIcon()} size={16} color="#6c5ce7" />
        <Text style={styles.audioModeText}>{getAudioModeText()}</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Concurrent Playback Demo</Text>
          <Text style={styles.infoText}>
            This screen demonstrates concurrent video playback. Multiple videos can play simultaneously without interrupting other device audio.
          </Text>
        </View>

        <View style={styles.controlsSection}>
          <TouchableOpacity style={styles.addButton} onPress={addCustomVideo}>
            <Ionicons name="add" size={20} color="white" />
            <Text style={styles.addButtonText}>Add Video URL</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.audioButton} 
            onPress={() => setShowAudioSettings(true)}
          >
            <Ionicons name={getAudioModeIcon()} size={20} color="white" />
            <Text style={styles.audioButtonText}>Audio Settings</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.videosSection}>
          {videos.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="videocam-off" size={48} color="#666" />
              <Text style={styles.emptyText}>No videos loaded</Text>
              <TouchableOpacity style={styles.emptyButton} onPress={addCustomVideo}>
                <Text style={styles.emptyButtonText}>Add Video</Text>
              </TouchableOpacity>
            </View>
          ) : (
            videos.map((video) => (
              <View key={video.id} style={styles.videoItem}>
                <View style={styles.videoHeader}>
                  <Text style={styles.videoTitle}>{video.title}</Text>
                  <TouchableOpacity 
                    style={styles.removeButton}
                    onPress={() => removeVideo(video.id)}
                  >
                    <Ionicons name="close" size={20} color="#ff4444" />
                  </TouchableOpacity>
                </View>
                
                <ConcurrentVideoPlayer
                  source={video.source}
                  title={video.title}
                  playerId={video.id}
                  onError={(error) => handleVideoError(error, video.id)}
                  showControls={true}
                  autoPlay={false}
                />
              </View>
            ))
          )}
        </View>

        <View style={styles.footerInfo}>
          <Text style={styles.footerText}>
            Videos playing: {videos.length}
          </Text>
          <Text style={styles.footerSubtext}>
            Each video can be controlled independently while maintaining concurrent playback support.
          </Text>
        </View>
      </ScrollView>

      <AudioMixingSettings
        visible={showAudioSettings}
        onClose={() => {
          setShowAudioSettings(false);
          loadAudioMode(); // Reload audio mode in case it changed
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#16213e',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  settingsButton: {
    padding: 8,
  },
  audioModeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
  },
  audioModeText: {
    color: '#6c5ce7',
    fontSize: 12,
    marginLeft: 4,
  },
  content: {
    flex: 1,
  },
  infoSection: {
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    margin: 16,
    borderRadius: 8,
  },
  infoTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  infoText: {
    color: '#ccc',
    fontSize: 14,
    lineHeight: 20,
  },
  controlsSection: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  addButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6c5ce7',
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  audioButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0f3460',
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  audioButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  videosSection: {
    paddingHorizontal: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
    marginTop: 12,
    marginBottom: 16,
  },
  emptyButton: {
    backgroundColor: '#6c5ce7',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  emptyButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  videoItem: {
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    overflow: 'hidden',
  },
  videoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  videoTitle: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    flex: 1,
  },
  removeButton: {
    padding: 4,
  },
  footerInfo: {
    padding: 16,
    alignItems: 'center',
  },
  footerText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  footerSubtext: {
    color: '#ccc',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
});

export default VideoPlayerScreen;
