import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import AsyncStorage from '@react-native-async-storage/async-storage';
import VideoDownloadService from './VideoDownloadService';

// Serviço para downloads em background
class BackgroundDownloadService {
  static activeDownloads = new Map(); // Controle de downloads ativos
  static downloadQueue = []; // Fila de downloads
  static isProcessing = false; // Flag para evitar processamento simultâneo

  // Iniciar download em background
  static async startBackgroundDownload(downloadInfo) {
    // Limpar downloads antigos primeiro
    await this.clearOldDownloads();

    const downloadId = this.generateDownloadId(downloadInfo);

    // Verificar se já está baixando
    if (this.activeDownloads.has(downloadId)) {
      console.log('⚠️ Download já em progresso:', downloadId);
      throw new Error('Este download já está em progresso');
    }

    // Verificar duplicatas apenas para URLs específicas de vídeo
    const url = downloadInfo.url || downloadInfo.originalUrl;
    if (this.shouldCheckForDuplicate(url)) {
      const isDuplicate = await this.checkForDuplicate(downloadInfo, downloadId);
      if (isDuplicate) {
        console.log('🚫 Download duplicado detectado:', downloadId);
        throw new Error('Este vídeo já foi baixado ou está sendo baixado');
      }
    }

    // Adicionar à fila
    const downloadTask = {
      id: downloadId,
      ...downloadInfo,
      status: 'queued',
      progress: 0,
      startTime: new Date().toISOString(),
      error: null,
      normalizedUrl: this.normalizeVideoUrl(downloadInfo.url || downloadInfo.originalUrl)
    };

    this.downloadQueue.push(downloadTask);
    this.activeDownloads.set(downloadId, downloadTask);

    // Salvar estado
    await this.saveDownloadState();

    // Processar fila
    this.processDownloadQueue();

    return downloadId;
  }

  // Verificar se deve checar duplicatas para esta URL
  static shouldCheckForDuplicate(url) {
    if (!url) return false;

    try {
      // Não verificar duplicatas para URLs de página genéricas
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();

      // URLs de página que não devem ser verificadas
      const genericPaths = ['/', '/home', '/feed', '/search', '/trending', '/explore'];
      if (genericPaths.includes(pathname)) {
        return false;
      }

      // Verificar apenas URLs que parecem ser de vídeos específicos
      const videoPatterns = [
        /\/watch\?v=/i,
        /\/video\//i,
        /\/embed\//i,
        /vimeo\.com\/\d+/i,
        /\.(mp4|webm|avi|mov)/i
      ];

      return videoPatterns.some(pattern => pattern.test(url));
    } catch (e) {
      return true; // Em caso de erro, verificar por segurança
    }
  }

  // Verificar se é um download duplicado
  static async checkForDuplicate(downloadInfo, downloadId) {
    try {
      const normalizedUrl = this.normalizeVideoUrl(downloadInfo.url || downloadInfo.originalUrl);
      const quality = downloadInfo.quality?.quality || 'default';

      // Verificar downloads ativos
      for (const [activeId, activeTask] of this.activeDownloads) {
        if (activeTask.normalizedUrl === normalizedUrl &&
            (activeTask.quality?.quality || 'default') === quality) {
          return true;
        }
      }

      // Verificar histórico de downloads
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];

      for (const download of history) {
        const downloadNormalizedUrl = this.normalizeVideoUrl(download.originalUrl);
        const downloadQuality = download.quality?.quality || 'default';

        if (downloadNormalizedUrl === normalizedUrl && downloadQuality === quality) {
          // Verificar se o arquivo ainda existe
          const FileSystem = require('expo-file-system');
          try {
            const fileInfo = await FileSystem.getInfoAsync(download.uri);
            if (fileInfo.exists && fileInfo.size > 10000) {
              return true;
            }
          } catch (e) {
            // Arquivo não existe, não é duplicata
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Erro ao verificar duplicata:', error);
      return false;
    }
  }

  // Gerar ID único para download baseado em URL normalizada
  static generateDownloadId(downloadInfo) {
    const url = downloadInfo.url || downloadInfo.originalUrl;
    const quality = downloadInfo.quality?.quality || 'default';

    // Normalizar URL para detectar duplicatas
    const normalizedUrl = this.normalizeVideoUrl(url);
    const urlHash = this.generateUrlHash(normalizedUrl);

    return `${urlHash}_${quality}`.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 50);
  }

  // Normalizar URL para comparação de duplicatas
  static normalizeVideoUrl(url) {
    try {
      const urlObj = new URL(url);

      // Remover parâmetros desnecessários
      const paramsToRemove = ['utm_source', 'utm_medium', 'utm_campaign', 'fbclid', 'gclid', 't', 'si'];
      paramsToRemove.forEach(param => urlObj.searchParams.delete(param));

      // Normalizar YouTube URLs
      if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
        const videoId = urlObj.searchParams.get('v') || urlObj.pathname.split('/').pop();
        if (videoId) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }

      // Normalizar outras plataformas
      if (urlObj.hostname.includes('vimeo.com')) {
        const videoId = urlObj.pathname.split('/').filter(p => p && /^\d+$/.test(p))[0];
        if (videoId) {
          return `https://vimeo.com/${videoId}`;
        }
      }

      // Para URLs diretas de vídeo, remover apenas parâmetros de tracking
      return urlObj.toString();
    } catch (e) {
      return url;
    }
  }

  // Gerar hash da URL para identificação única
  static generateUrlHash(url) {
    let hash = 0;
    for (let i = 0; i < url.length; i++) {
      const char = url.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Processar fila de downloads
  static async processDownloadQueue() {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      while (this.downloadQueue.length > 0) {
        const downloadTask = this.downloadQueue.shift();
        
        if (!downloadTask) continue;

        console.log(`🚀 Iniciando download em background: ${downloadTask.id}`);
        
        try {
          await this.executeDownload(downloadTask);
        } catch (error) {
          console.error(`❌ Erro no download ${downloadTask.id}:`, error);
          downloadTask.status = 'error';
          downloadTask.error = error.message;
          await this.saveDownloadState();
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  // Executar download individual
  static async executeDownload(downloadTask) {
    try {
      downloadTask.status = 'downloading';
      await this.saveDownloadState();

      // Gerar nome do arquivo
      const fileName = this.generateFileName(downloadTask);
      const fileUri = FileSystem.documentDirectory + fileName;

      // Callback de progresso
      const progressCallback = async (progress) => {
        const percentage = Math.round((progress.totalBytesWritten / progress.totalBytesExpectedToWrite) * 100);
        downloadTask.progress = percentage;
        downloadTask.status = 'downloading';

        // Salvar progresso a cada 10%
        if (percentage % 10 === 0) {
          await this.saveDownloadState();
        }
      };

      // Usar VideoDownloadService para fazer o download real
      const result = await VideoDownloadService.downloadVideo(
        downloadTask.url,
        progressCallback
      );

      if (!result || !result.uri) {
        throw new Error('Falha no download do arquivo');
      }

      // Salvar na galeria usando o VideoDownloadService
      try {
        await VideoDownloadService.saveToGallery(result.uri, result.fileName || fileName);
      } catch (galleryError) {
        // Se falhar ao salvar na galeria, continuar mesmo assim
        console.warn('Falha ao salvar na galeria:', galleryError.message);
      }

      // Atualizar status
      downloadTask.status = 'completed';
      downloadTask.progress = 100;
      downloadTask.fileUri = result.uri;
      downloadTask.fileName = result.fileName || fileName;
      downloadTask.fileSize = result.size;
      downloadTask.completedTime = new Date().toISOString();

      // Adicionar ao histórico de downloads
      await this.addToDownloadHistory(downloadTask);

    } catch (error) {
      downloadTask.status = 'error';
      downloadTask.error = error.message;
      throw error;
    } finally {
      await this.saveDownloadState();
    }
  }

  // Gerar nome do arquivo
  static generateFileName(downloadTask) {
    const quality = downloadTask.quality?.quality || 'default';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = this.getFileExtension(downloadTask.url);
    
    return `video_${quality}_${timestamp}.${extension}`;
  }

  // Obter extensão do arquivo
  static getFileExtension(url) {
    if (url.includes('.mp4')) return 'mp4';
    if (url.includes('.webm')) return 'webm';
    if (url.includes('.avi')) return 'avi';
    if (url.includes('.mov')) return 'mov';
    return 'mp4'; // padrão
  }

  // Salvar na galeria
  static async saveToGallery(fileUri, fileName) {
    try {
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      const album = await MediaLibrary.getAlbumAsync('Video Downloads');
      
      if (album == null) {
        await MediaLibrary.createAlbumAsync('Video Downloads', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }
      
      console.log('✅ Vídeo salvo na galeria:', fileName);
    } catch (error) {
      console.error('❌ Erro ao salvar na galeria:', error);
      throw error;
    }
  }

  // Salvar estado dos downloads
  static async saveDownloadState() {
    try {
      const state = {
        activeDownloads: Array.from(this.activeDownloads.values()),
        queue: this.downloadQueue,
        lastUpdate: new Date().toISOString()
      };
      
      await AsyncStorage.setItem('background_downloads', JSON.stringify(state));
    } catch (error) {
      console.error('Erro ao salvar estado dos downloads:', error);
    }
  }

  // Carregar estado dos downloads
  static async loadDownloadState() {
    try {
      const stateJson = await AsyncStorage.getItem('background_downloads');
      if (stateJson) {
        const state = JSON.parse(stateJson);
        
        // Restaurar downloads ativos
        this.activeDownloads.clear();
        state.activeDownloads?.forEach(download => {
          this.activeDownloads.set(download.id, download);
        });
        
        // Restaurar fila (apenas downloads pendentes)
        this.downloadQueue = state.queue?.filter(d => d.status === 'queued') || [];
        
        console.log(`📂 Estado restaurado: ${this.activeDownloads.size} downloads ativos`);
      }
    } catch (error) {
      console.error('Erro ao carregar estado dos downloads:', error);
    }
  }

  // Adicionar ao histórico
  static async addToDownloadHistory(downloadTask) {
    try {
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];
      
      const historyItem = {
        id: downloadTask.id,
        fileName: downloadTask.fileName,
        uri: downloadTask.fileUri,
        size: downloadTask.fileSize,
        quality: downloadTask.quality,
        originalUrl: downloadTask.originalUrl,
        timestamp: downloadTask.completedTime,
        status: 'completed'
      };
      
      history.unshift(historyItem);
      await AsyncStorage.setItem('video_downloads', JSON.stringify(history.slice(0, 100))); // Manter apenas 100
      
    } catch (error) {
      console.error('Erro ao adicionar ao histórico:', error);
    }
  }

  // Obter status de todos os downloads
  static getDownloadStatus() {
    return {
      active: Array.from(this.activeDownloads.values()),
      queue: this.downloadQueue.length,
      processing: this.isProcessing
    };
  }

  // Cancelar download
  static async cancelDownload(downloadId) {
    const download = this.activeDownloads.get(downloadId);
    if (download) {
      download.status = 'cancelled';
      this.activeDownloads.delete(downloadId);
      
      // Remover da fila se estiver lá
      this.downloadQueue = this.downloadQueue.filter(d => d.id !== downloadId);
      
      await this.saveDownloadState();
      return true;
    }
    return false;
  }

  // Limpar downloads concluídos
  static async clearCompletedDownloads() {
    const completed = Array.from(this.activeDownloads.values()).filter(d =>
      d.status === 'completed' || d.status === 'error'
    );

    completed.forEach(download => {
      this.activeDownloads.delete(download.id);
    });

    await this.saveDownloadState();
    return completed.length;
  }

  // Limpar downloads antigos (mais de 1 hora)
  static async clearOldDownloads() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const oldDownloads = Array.from(this.activeDownloads.values()).filter(d => {
      const startTime = new Date(d.startTime).getTime();
      return startTime < oneHourAgo;
    });

    oldDownloads.forEach(download => {
      this.activeDownloads.delete(download.id);
    });

    if (oldDownloads.length > 0) {
      await this.saveDownloadState();
    }
  }

  // Inicializar serviço
  static async initialize() {
    await this.loadDownloadState();
    
    // Retomar downloads pendentes
    if (this.downloadQueue.length > 0) {
      console.log(`🔄 Retomando ${this.downloadQueue.length} downloads pendentes`);
      this.processDownloadQueue();
    }
  }
}

export default BackgroundDownloadService;
