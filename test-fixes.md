# Testing Guide for Video Downloader Fixes

## 1. Duplicate Download Prevention Testing

### Test Cases:
1. **Same Video, Same Quality**
   - Navigate to a video page (e.g., YouTube)
   - Download a video in 720p
   - Try to download the same video in 720p again
   - Expected: Should show error "Este vídeo já foi baixado ou está sendo baixado"

2. **Same Video, Different Quality**
   - Download a video in 720p
   - Try to download the same video in 1080p
   - Expected: Should allow download (different quality)

3. **Different URLs, Same Video**
   - Download a YouTube video using youtube.com URL
   - Try to download the same video using youtu.be URL
   - Expected: Should detect as duplicate and prevent

4. **Background Download Duplicates**
   - Start a background download
   - Try to start another download of the same video while first is in progress
   - Expected: Should show error "Este download já está em progresso"

### How to Test:
1. Open the app
2. Navigate to a video website
3. Download a video
4. Check Downloads page to confirm it's saved
5. Try to download the same video again
6. Verify duplicate prevention works

## 2. Thumbnail Video Download Testing

### Test Cases:
1. **YouTube Thumbnails**
   - Go to YouTube homepage or search results
   - Long-press on video thumbnails
   - Expected: Context menu should appear with download options

2. **Social Media Thumbnails**
   - Visit Instagram, TikTok, or Facebook
   - Long-press on video thumbnails/previews
   - Expected: Context menu should appear

3. **Generic Website Videos**
   - Visit news sites or blogs with video content
   - Long-press on video thumbnails
   - Expected: Context menu should appear

4. **Quality Selection from Thumbnails**
   - Long-press a thumbnail
   - Select "Escolher Qualidade"
   - Expected: Quality selector should open
   - Select a quality and confirm download starts

5. **Quick Download from Thumbnails**
   - Long-press a thumbnail
   - Select "Download Rápido"
   - Expected: Download should start immediately in best quality

### How to Test:
1. Open the app browser
2. Navigate to video websites
3. Look for video thumbnails
4. Long-press on thumbnails (mobile) or right-click (desktop)
5. Verify context menu appears
6. Test both quick download and quality selection
7. Check Downloads page to confirm videos are saved

## 3. Integration Testing

### Test Cases:
1. **Mixed Downloads**
   - Download main video from a page
   - Download thumbnail video from same page
   - Expected: Both should work without conflicts

2. **Cross-Platform Testing**
   - Test on different video platforms
   - Verify thumbnail detection works universally
   - Check that platform-specific URLs are handled correctly

3. **Error Handling**
   - Try downloading from invalid URLs
   - Test with broken video links
   - Expected: Proper error messages should appear

### Expected Behaviors:
- ✅ No duplicate downloads of same video/quality
- ✅ Thumbnail context menu appears on long-press/right-click
- ✅ Quality selection works for thumbnail downloads
- ✅ Downloads appear in Downloads page with proper metadata
- ✅ Background downloads work without blocking UI
- ✅ Proper error messages for failed downloads

## 4. Debugging

### Console Logs to Watch:
- `[INJECT]` - JavaScript injection and thumbnail detection
- `[CONTEXT_MENU]` - Context menu operations
- `🚫 Download duplicado detectado` - Duplicate prevention
- `🚀 Iniciando download` - Download initiation
- `❌ Erro no download` - Download errors

### Common Issues:
1. **Context menu not appearing**: Check console for `[INJECT]` logs
2. **Duplicate prevention not working**: Check for URL normalization logs
3. **Downloads not saving**: Check permissions and file system access
4. **Quality selection failing**: Check video URL processing logs

## 5. Performance Considerations

- Duplicate checking should be fast (< 100ms)
- Thumbnail detection should not slow down page loading
- Background downloads should not interfere with browsing
- Memory usage should remain stable during multiple downloads
