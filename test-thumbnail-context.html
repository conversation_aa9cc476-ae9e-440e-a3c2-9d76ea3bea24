<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Thumbnail Context Menu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .video-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .video-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            width: 300px;
        }
        .video-thumbnail {
            width: 100%;
            height: 180px;
            background-color: #ddd;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #666;
            margin-bottom: 10px;
        }
        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }
        .video-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .video-duration {
            color: #666;
            font-size: 14px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>Test Thumbnail Context Menu</h2>
        <p><strong>Instructions:</strong></p>
        <ul>
            <li><strong>Mobile:</strong> Long press on any video thumbnail to trigger the context menu</li>
            <li><strong>Desktop:</strong> Right-click on any video thumbnail to trigger the context menu</li>
        </ul>
        <p>Check the browser console for debugging information.</p>
    </div>

    <div class="video-container">
        <div class="video-item">
            <img class="video-thumbnail" src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" alt="YouTube Video" data-video-url="https://www.youtube.com/watch?v=dQw4w9WgXcQ">
            <div class="video-title">Sample YouTube Video (IMG element)</div>
            <div class="video-duration">3:32</div>
        </div>

        <div class="video-item">
            <div class="video-thumbnail" data-video-url="https://vimeo.com/123456789">
                ▶️ DIV with data-video-url
            </div>
            <div class="video-title">Sample Vimeo Video</div>
            <div class="video-duration">5:45</div>
        </div>

        <div class="video-item">
            <a href="https://www.youtube.com/watch?v=example123" class="video-link">
                <div class="video-thumbnail">
                    ▶️ Link to YouTube
                </div>
                <div class="video-title">Linked Video Thumbnail</div>
                <div class="video-duration">4:22</div>
            </a>
        </div>

        <div class="video-item">
            <video class="video-thumbnail" width="300" height="180" controls>
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <div class="video-title">HTML5 Video Element</div>
            <div class="video-duration">0:10</div>
        </div>
    </div>

    <script>
        // Simple test to verify the page loads
        console.log('[TEST] Test page loaded successfully');
        
        // Add some basic interaction logging
        document.addEventListener('DOMContentLoaded', function() {
            const thumbnails = document.querySelectorAll('.video-thumbnail');
            console.log('[TEST] Found ' + thumbnails.length + ' video thumbnails');
            
            thumbnails.forEach((thumb, index) => {
                thumb.addEventListener('click', function(e) {
                    console.log('[TEST] Thumbnail ' + index + ' clicked');
                });
                
                thumb.addEventListener('contextmenu', function(e) {
                    console.log('[TEST] Thumbnail ' + index + ' right-clicked');
                });
                
                thumb.addEventListener('touchstart', function(e) {
                    console.log('[TEST] Thumbnail ' + index + ' touch started');
                });
            });
        });
    </script>
</body>
</html>
