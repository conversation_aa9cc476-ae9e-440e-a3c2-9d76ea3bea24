import { Alert, Linking, Platform } from 'react-native';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';

class PermissionManager {
  constructor() {
    this.permissionStatus = {
      mediaLibrary: null,
      storage: null
    };
  }

  // Verificar todas as permissões necessárias
  async checkAllPermissions() {
    try {
      const mediaLibraryStatus = await MediaLibrary.getPermissionsAsync();
      
      this.permissionStatus = {
        mediaLibrary: mediaLibraryStatus.status,
        storage: 'granted' // FileSystem não requer permissão explícita no Expo
      };

      return {
        allGranted: mediaLibraryStatus.status === 'granted',
        permissions: this.permissionStatus
      };
    } catch (error) {
      console.error('Erro ao verificar permissões:', error);
      return {
        allGranted: false,
        permissions: this.permissionStatus,
        error: error.message
      };
    }
  }

  // Solicitar permissão da galeria de mídia
  async requestMediaLibraryPermission() {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      this.permissionStatus.mediaLibrary = status;
      return status === 'granted';
    } catch (error) {
      console.error('Erro ao solicitar permissão da galeria:', error);
      return false;
    }
  }

  // Solicitar permissão de armazenamento (alias para compatibilidade)
  async requestStoragePermission() {
    try {
      // No Expo, storage permission é principalmente sobre MediaLibrary
      const mediaLibraryGranted = await this.requestMediaLibraryPermission();

      this.permissionStatus.storage = mediaLibraryGranted ? 'granted' : 'denied';

      return mediaLibraryGranted;
    } catch (error) {
      console.error('Erro ao solicitar permissão de armazenamento:', error);
      this.permissionStatus.storage = 'denied';
      return false;
    }
  }

  // Solicitar todas as permissões necessárias
  async requestAllPermissions() {
    const mediaLibraryGranted = await this.requestMediaLibraryPermission();
    
    return {
      allGranted: mediaLibraryGranted,
      mediaLibrary: mediaLibraryGranted,
      storage: true // FileSystem sempre disponível no Expo
    };
  }

  // Mostrar diálogo explicativo sobre permissões
  showPermissionExplanation() {
    return new Promise((resolve) => {
      Alert.alert(
        'Permissões Necessárias',
        'Este aplicativo precisa de acesso à galeria para salvar os vídeos baixados.\n\n' +
        '• Galeria: Para salvar vídeos baixados\n' +
        '• Armazenamento: Para gerenciar arquivos temporários\n\n' +
        'Suas informações pessoais não serão acessadas.',
        [
          {
            text: 'Cancelar',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Conceder Permissões',
            onPress: () => resolve(true)
          }
        ]
      );
    });
  }

  // Mostrar diálogo para ir às configurações
  showSettingsDialog() {
    return new Promise((resolve) => {
      Alert.alert(
        'Permissões Negadas',
        'Para usar este aplicativo, você precisa conceder permissão de acesso à galeria.\n\n' +
        'Vá para Configurações > Aplicativos > Video Downloader > Permissões e ative o acesso à galeria.',
        [
          {
            text: 'Cancelar',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Abrir Configurações',
            onPress: () => {
              Linking.openSettings();
              resolve(true);
            }
          }
        ]
      );
    });
  }

  // Verificar e solicitar permissões com UX amigável
  async ensurePermissions() {
    try {
      // Primeiro, verificar permissões atuais
      const currentStatus = await this.checkAllPermissions();
      
      if (currentStatus.allGranted) {
        return { success: true, message: 'Todas as permissões concedidas' };
      }

      // Mostrar explicação sobre por que precisamos das permissões
      const userAccepted = await this.showPermissionExplanation();
      
      if (!userAccepted) {
        return { 
          success: false, 
          message: 'Permissões são necessárias para salvar vídeos',
          cancelled: true 
        };
      }

      // Solicitar permissões
      const permissionResult = await this.requestAllPermissions();
      
      if (permissionResult.allGranted) {
        return { success: true, message: 'Permissões concedidas com sucesso' };
      }

      // Se ainda não temos permissões, mostrar diálogo para configurações
      const goToSettings = await this.showSettingsDialog();
      
      return { 
        success: false, 
        message: 'Permissões necessárias não foram concedidas',
        needsSettings: true 
      };

    } catch (error) {
      console.error('Erro ao gerenciar permissões:', error);
      return { 
        success: false, 
        message: 'Erro ao verificar permissões: ' + error.message,
        error: true 
      };
    }
  }

  // Verificar se o diretório de downloads existe e é acessível
  async checkStorageAccess() {
    try {
      const documentsDir = FileSystem.documentDirectory;
      const dirInfo = await FileSystem.getInfoAsync(documentsDir);

      if (!dirInfo.exists) {
        return { accessible: false, message: 'Diretório de documentos não encontrado' };
      }

      // Tentar criar um arquivo de teste
      const testFile = documentsDir + 'test_write.txt';
      await FileSystem.writeAsStringAsync(testFile, 'test');

      // Verificar se o arquivo foi criado
      const testFileInfo = await FileSystem.getInfoAsync(testFile);

      if (testFileInfo.exists) {
        // Limpar arquivo de teste (ignorar erros)
        try {
          await FileSystem.deleteAsync(testFile, { idempotent: true });
        } catch (deleteError) {
          // Ignorar erro de delete
        }
        return { accessible: true, message: 'Armazenamento acessível' };
      }

      return { accessible: false, message: 'Não foi possível escrever no armazenamento' };

    } catch (error) {
      // No Expo, assumir que o armazenamento funciona
      return {
        accessible: true,
        message: 'Armazenamento assumido como acessível'
      };
    }
  }

  // Obter informações sobre o espaço disponível
  async getStorageInfo() {
    try {
      const documentsDir = FileSystem.documentDirectory;
      const dirInfo = await FileSystem.getInfoAsync(documentsDir);
      
      return {
        available: dirInfo.exists,
        path: documentsDir,
        // Note: Expo não fornece informações de espaço livre diretamente
        message: dirInfo.exists ? 'Armazenamento disponível' : 'Armazenamento não disponível'
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }
}

export default new PermissionManager();
